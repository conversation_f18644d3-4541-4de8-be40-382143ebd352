import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import MockAdapter from 'axios-mock-adapter';
import { toast } from '@/hooks/use-toast';

// PostHog will be imported dynamically to avoid SSR issues
let posthog: any = null;
if (typeof window !== 'undefined') {
  import('posthog-js').then(module => {
    posthog = module.default;
  });
}

interface ApiConfig<T = any> extends AxiosRequestConfig {
  mock?: boolean;
  mockData?: T[];
}

// PostHog logging utilities
const logFailedRequest = (error: AxiosError, requestId: string) => {
  // Only log to PostHog in production, not in local development
  const isDevelopment = process.env.NODE_ENV === 'development';
  const isLocalhost =
    typeof window !== 'undefined' &&
    (window.location.hostname === 'localhost' ||
      window.location.hostname === '127.0.0.1' ||
      window.location.hostname.includes('localhost'));

  // Skip logging in development, localhost, or if PostHog not properly initialized
  if (!posthog || !posthog.__loaded || isDevelopment || isLocalhost) return;

  const request = error.config;
  const response = error.response;

  // Prepare detailed request information
  const requestData = {
    request_id: requestId,
    timestamp: new Date().toISOString(),
    method: request?.method?.toUpperCase(),
    url: request?.url,
    full_url: request?.baseURL ? `${request.baseURL}${request.url}` : request?.url,
    headers: request?.headers,
    request_body: request?.data,
    params: request?.params,
    timeout: request?.timeout,
    status_code: response?.status,
    status_text: response?.statusText,
    response_headers: response?.headers,
    response_body: response?.data,
    error_message: error.message,
    error_code: error.code,
    network_error: !response, // true if network error, false if HTTP error
    user_agent: typeof window !== 'undefined' ? navigator.userAgent : null,
    page_url: typeof window !== 'undefined' ? window.location.href : null
  };

  // Generate curl command for debugging
  const curlCommand = generateCurlCommand(request);

  // Log to PostHog using custom event name (simpler and more reliable)
  posthog.capture('api_request_failed', {
    ...requestData,
    curl_command: curlCommand,
    event_type: 'error',
    error_category: 'api_failure',
    debug_info: {
      can_regenerate_with_curl: true,
      suggested_action: 'Copy curl command and test in terminal or send to AI for analysis'
    }
  });
};

const generateCurlCommand = (config: any): string => {
  if (!config) return '';

  const method = config.method?.toUpperCase() || 'GET';
  const url = config.baseURL ? `${config.baseURL}${config.url}` : config.url;
  const headers = config.headers || {};

  let curl = `curl -X ${method}`;

  // Add headers
  Object.entries(headers).forEach(([key, value]) => {
    if (
      key !== 'common' &&
      key !== 'delete' &&
      key !== 'get' &&
      key !== 'head' &&
      key !== 'post' &&
      key !== 'put' &&
      key !== 'patch'
    ) {
      curl += ` -H "${key}: ${value}"`;
    }
  });

  // Add request body for POST/PUT/PATCH
  if (config.data && ['POST', 'PUT', 'PATCH'].includes(method)) {
    const dataStr = typeof config.data === 'string' ? config.data : JSON.stringify(config.data);
    curl += ` -d '${dataStr}'`;
  }

  // Add URL with params
  if (config.params) {
    const params = new URLSearchParams(config.params).toString();
    curl += ` "${url}?${params}"`;
  } else {
    curl += ` "${url}"`;
  }

  return curl;
};

const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Create base axios instance
const baseApi = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'https://be-dev.ttmi.pro/api',
  headers: {
    'Content-Type': 'application/json'
  }
});

console.log('API URL:', process.env.NEXT_PUBLIC_API_URL || 'https://be-dev.ttmi.pro/api');

class EnhancedAxiosInstance {
  private instance = baseApi;
  private mockAdapter: MockAdapter | null = null;

  private setupMockAdapter(_config: ApiConfig) {
    if (!this.mockAdapter) {
      this.mockAdapter = new MockAdapter(this.instance, {
        delayResponse: 300,
        onNoMatch: 'passthrough'
      });
    }
    return this.mockAdapter;
  }

  private createMockResponse<T>(mockData?: T[], _url?: string, _method?: string): any {
    if (mockData && mockData.length > 0) {
      const response = {
        count: mockData.length,
        next: null,
        previous: null,
        results: mockData
      };
      return response;
    }

    const emptyResponse = {
      count: 0,
      next: null,
      previous: null,
      results: []
    };
    return emptyResponse;
  }

  async get<T = any>(url: string, config?: ApiConfig): Promise<AxiosResponse<T>> {
    if (config?.mock === true) {
      const mock = this.setupMockAdapter(config);
      const mockResponse = this.createMockResponse(config.mockData, url, 'GET');

      mock.onGet(url).reply(200, mockResponse);

      try {
        const { mock: _, mockData: __, ...cleanConfig } = config;
        const response = await this.instance.get<T>(url, cleanConfig);
        mock.reset();
        return response;
      } catch (error) {
        mock.reset();
        throw error;
      }
    }
    return this.instance.get<T>(url, config);
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.instance.post<T>(url, data, config);
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.instance.put<T>(url, data, config);
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.instance.delete<T>(url, config);
  }

  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.instance.patch<T>(url, data, config);
  }

  get interceptors() {
    return this.instance.interceptors;
  }
  resetMock() {
    if (this.mockAdapter) {
      this.mockAdapter.reset();
    }
  }
}

// Create enhanced API instance
const api = new EnhancedAxiosInstance();

// Add request interceptor to include auth token and request ID
api.interceptors.request.use(
  config => {
    // Add auth token
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers['Authorization'] = `Token ${token}`;
    }

    // Add unique request ID for tracking
    const requestId = generateRequestId();
    config.metadata = { requestId, startTime: Date.now() };

    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle errors and log failures
api.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    // Get request ID from metadata
    const requestId = error.config?.metadata?.requestId || generateRequestId();

    // Log failed request to PostHog (PRODUCTION ONLY)
    logFailedRequest(error, requestId);

    // Handle auth errors
    if (error.response?.status === 401) {
      // Clear token and redirect to login on auth error
      localStorage.removeItem('auth_token');
      window.location.href = '/xac-thuc';
    }

    return Promise.reject(error);
  }
);

api.interceptors.response.use(
  res => {
    const data = res?.data;
    const method = res?.config?.method?.toLowerCase();
    const shouldToastSuccess = method === 'post' || method === 'put' || method === 'delete';

    if (shouldToastSuccess) {
      if (data?.message) {
        toast({
          title: 'Thành công',
          description: typeof data.message === 'string' ? data.message : 'Cập nhật thành công',
          variant: 'default',
          duration: 3000
        });
      } else {
        toast({
          title: 'Thành công',
          variant: 'default',
          duration: 3000
        });
      }
    }

    return res;
  },
  error => {
    const status = error?.response?.status;
    const data = error?.response?.data;
    let description = 'Có lỗi xảy ra';
    if (status === 500) {
      description = 'Có lỗi xảy ra, vui lòng liên hệ với quản trị viên';
    } else if (status === 404) {
      description = '404 Not Found';
    } else if (data) {
      if (typeof data === 'string') {
        description = data;
      } else if (data.detail) {
        description = data.detail;
      } else if (data.message) {
        // Handle case where data.message is an object (validation errors)
        if (typeof data.message === 'object' && data.message !== null) {
          description = Object.entries(data.message)
            .map(([field, messages]) => {
              if (Array.isArray(messages)) {
                return messages.map(msg => `${field}: ${msg}`).join('\n');
              }
              return `${field}: ${messages}`;
            })
            .join('\n');
        } else {
          description = data.message;
        }
      } else if (typeof data === 'object') {
        description = Object.entries(data)
          .map(([field, messages]) => {
            if (Array.isArray(messages)) {
              return messages.map(msg => `${field}: ${msg}`).join('\n');
            }
            return `${field}: ${messages}`;
          })
          .join('\n');
      }
    }

    toast({
      title: 'Lỗi',
      description,
      variant: 'destructive',
      duration: 3000
    });

    return Promise.reject(error);
  }
);

export default api;
